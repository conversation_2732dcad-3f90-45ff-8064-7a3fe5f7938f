<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview - Pilihan Metode Pembayaran</title>
    <link rel="stylesheet" href="css/payment.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <section id="payment">
        <div class="payment-container">
            <h2 class="payment-title">Cara Pembayaran</h2>
            <p class="payment-subtitle">Proses mudah dalam 4 langkah</p>
            
            <div class="payment-steps">
                <div class="step-item">
                    <div class="step-icon"><i class="fas fa-shopping-cart"></i></div>
                    <span class="step-label">Masukkan Keranjang</span>
                </div>
                <div class="step-item">
                    <div class="step-icon"><i class="fas fa-file-invoice"></i></div>
                    <span class="step-label">Checkout</span>
                </div>
                <div class="step-item">
                    <div class="step-icon"><i class="fas fa-upload"></i></div>
                    <span class="step-label">Kirim Bukti Bayar</span>
                </div>
                <div class="step-item">
                    <div class="step-icon"><i class="fas fa-check-circle"></i></div>
                    <span class="step-label">Konfirmasi</span>
                </div>
            </div>

            <div class="payment-form">
                <h3>Konfirmasi Pembayaran</h3>
                <form id="paymentForm" method="POST" enctype="multipart/form-data">
                    <input class="kirim" type="text" name="order_number" placeholder="Nomor Order" required />
                    <input type="file" name="bukti_bayar" accept=".jpg, .jpeg, .png" required />
                    
                    <div class="payment-methods">
                        <label>Pilih Metode Pembayaran</label>
                        <div class="payment-options">
                            <!-- Bank Virtual Account -->
                            <div class="payment-option" data-method="bca" data-number="**********">
                                <div class="payment-logo">
                                    <img src="img/payment/bca.svg" alt="BCA" />
                                </div>
                                <div class="payment-info">
                                    <span class="bank-name">BCA Virtual Account</span>
                                    <span class="account-number">**********</span>
                                </div>
                            </div>

                            <div class="payment-option" data-method="mandiri" data-number="**********">
                                <div class="payment-logo">
                                    <img src="img/payment/mandiri.svg" alt="Mandiri" />
                                </div>
                                <div class="payment-info">
                                    <span class="bank-name">Mandiri Virtual Account</span>
                                    <span class="account-number">**********</span>
                                </div>
                            </div>

                            <div class="payment-option" data-method="bni" data-number="**********">
                                <div class="payment-logo">
                                    <img src="img/payment/bni.svg" alt="BNI" />
                                </div>
                                <div class="payment-info">
                                    <span class="bank-name">BNI Virtual Account</span>
                                    <span class="account-number">**********</span>
                                </div>
                            </div>

                            <!-- E-Money -->
                            <div class="payment-option" data-method="gopay" data-number="08**********">
                                <div class="payment-logo">
                                    <img src="img/payment/gopay.svg" alt="GoPay" />
                                </div>
                                <div class="payment-info">
                                    <span class="bank-name">GoPay</span>
                                    <span class="account-number">08**********</span>
                                </div>
                            </div>

                            <div class="payment-option" data-method="ovo" data-number="************">
                                <div class="payment-logo">
                                    <img src="img/payment/ovo.svg" alt="OVO" />
                                </div>
                                <div class="payment-info">
                                    <span class="bank-name">OVO</span>
                                    <span class="account-number">************</span>
                                </div>
                            </div>

                            <div class="payment-option" data-method="dana" data-number="************">
                                <div class="payment-logo">
                                    <img src="img/payment/dana.svg" alt="DANA" />
                                </div>
                                <div class="payment-info">
                                    <span class="bank-name">DANA</span>
                                    <span class="account-number">************</span>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="selected_payment_method" id="selectedPaymentMethod" required />
                        <input type="hidden" name="selected_account_number" id="selectedAccountNumber" required />
                    </div>
                    
                    <button class="kirim" type="submit">Kirim Bukti Pembayaran</button>
                </form>
            </div>
        </div>
    </section>

    <script>
        // Handle payment method selection
        document.querySelectorAll('.payment-option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove active class from all options
                document.querySelectorAll('.payment-option').forEach(opt => opt.classList.remove('active'));
                
                // Add active class to selected option
                this.classList.add('active');
                
                // Set hidden input values
                document.getElementById('selectedPaymentMethod').value = this.dataset.method;
                document.getElementById('selectedAccountNumber').value = this.dataset.number;
                
                // Show selected method info
                console.log('Selected:', this.dataset.method, this.dataset.number);
            });
        });

        document.getElementById('paymentForm').addEventListener('submit', function(event) {
            event.preventDefault();
            
            // Check if payment method is selected
            if (!document.getElementById('selectedPaymentMethod').value) {
                alert('Silakan pilih metode pembayaran terlebih dahulu!');
                return;
            }
            
            alert('Form submitted! Selected method: ' + document.getElementById('selectedPaymentMethod').value);
        });
    </script>
</body>
</html>
