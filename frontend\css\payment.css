* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Roboto", sans-serif;
  background-color: #1e1e1e;
  color: #f1f1f1;
}

#payment {
  padding: 50px 50px;
  background-color: #111111;
  color: #f1f1f1;
}

.payment-container {
  max-width: 1200px;
  margin: 0 auto;
}

.payment-title {
  font-size: 32px;
  font-weight: 600;
  color: #f1c40f;
  text-align: center;
  margin-bottom: 20px;
}

.payment-subtitle {
  font-size: 20px;
  font-weight: 400;
  color: #fff;
  text-align: center;
  margin-bottom: 40px;
}

.payment-steps {
  display: flex;
  justify-content: space-between;
  gap: 30px;
  margin-bottom: 50px;
}

.step-item {
  text-align: center;
  flex: 1;
}

.step-icon {
  font-size: 40px;
  color: #f1c40f;
  margin-bottom: 10px;
}

.step-label {
  font-size: 18px;
  font-weight: 500;
  color: #fff;
}

.transaction-details {
  background-color: #2c3e50;
  padding: 20px;
  border-radius: 10px;
}

.transaction-details h3 {
  font-size: 24px;
  font-weight: 600;
  color: #f1c40f;
  margin-bottom: 20px;
}

.cart-table {
  width: 100%;
  border-collapse: collapse;
}

.cart-table th,
.cart-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #444;
}

.cart-table th {
  color: #f1c40f;
}

.cart-table td {
  color: #fff;
}

.cart-table a {
  color: #e67e22;
  text-decoration: none;
}

.cart-table a:hover {
  text-decoration: underline;
}

.payment-form {
  background-color: #2c3e50;
  padding: 20px;
  border-radius: 10px;
  margin-top: 40px;
}

.payment-form h3 {
  font-size: 24px;
  font-weight: 600;
  color: #f1c40f;
  margin-bottom: 20px;
}

.payment-form input,
.payment-form button {
  width: 100%;
  padding: 15px;
  margin-bottom: 20px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
}

.payment-form input[type="text"] {
  background-color: #34495e;
  color: #fff;
}

.payment-form input[type="file"] {
  background-color: #34495e;
  color: #fff;
}

.payment-form button {
  background-color: #f39c12;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.payment-form button:hover {
  background-color: #e67e22;
}

/* Payment Methods Styling */
.payment-methods {
  margin-bottom: 20px;
}

.payment-methods label {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #f1c40f;
  margin-bottom: 15px;
}

.payment-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #34495e;
  border: 2px solid transparent;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.payment-option:hover {
  border-color: #f1c40f;
  background-color: #3c5a78;
}

.payment-option.active {
  border-color: #f1c40f;
  background-color: #3c5a78;
  box-shadow: 0 0 10px rgba(241, 196, 15, 0.3);
}

.payment-logo {
  width: 60px;
  height: 40px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-logo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.payment-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.bank-name {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 5px;
}

.account-number {
  font-size: 14px;
  color: #bdc3c7;
  font-family: 'Courier New', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-options {
    grid-template-columns: 1fr;
  }

  .payment-option {
    padding: 12px;
  }

  .payment-logo {
    width: 50px;
    height: 35px;
    margin-right: 12px;
  }

  .bank-name {
    font-size: 14px;
  }

  .account-number {
    font-size: 12px;
  }
}
