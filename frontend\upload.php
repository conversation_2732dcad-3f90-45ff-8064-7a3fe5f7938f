<?php
if (!isset($_COOKIE['auth_token'])) {
    header("Location: login.php");
    exit();
}
$token = $_COOKIE['auth_token'];
$lockDir = 'locks/';
$lockFile = $lockDir . $token . '.lock';
if (!preg_match('/^[a-f0-9]{64}$/', $token)) {
    die("Token tidak valid");
}
if (!file_exists($lockFile)) {
    header("Location: login.php");
    exit();
} 
$userId = (int)file_get_contents($lockFile);
require 'dbcon.php';

if (isset($_FILES['bukti_bayar']) && $_FILES['bukti_bayar']['error'] === UPLOAD_ERR_OK) {
    $fileTmpPath = $_FILES['bukti_bayar']['tmp_name'];
    $fileName = $_FILES['bukti_bayar']['name'];
    $fileSize = $_FILES['bukti_bayar']['size'];
    $fileType = $_FILES['bukti_bayar']['type'];
    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    $allowedExt = ['jpg', 'jpeg', 'png'];
    if (!in_array($fileExt, $allowedExt)) {
        die("Ekstensi file tidak valid. Harus jpg, jpeg, atau png.");
    }
    $uploadDir = 'C:/xampp/htdocs/parfum/uploads/'; //rubah lokasi sesuai dengan xampp misal D:/~~~
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    $newFileName = uniqid() . '.' . $fileExt;
    $uploadPath = $uploadDir . $newFileName;
    if (move_uploaded_file($fileTmpPath, $uploadPath)) {
        $orderNumber = mysqli_real_escape_string($connection, $_POST['order_number']);
        $paymentMethod = mysqli_real_escape_string($connection, $_POST['selected_payment_method']);
        $accountNumber = mysqli_real_escape_string($connection, $_POST['selected_account_number']);

        $queryCheck = "SELECT * FROM pembelian WHERE kode_pembelian = '$orderNumber' AND user_id = $userId";
        $resultCheck = mysqli_query($connection, $queryCheck);
        if (mysqli_num_rows($resultCheck) > 0) {
            $query = "UPDATE pembelian SET bukti_bayar = '$uploadPath', metode_pembayaran = '$paymentMethod', nomor_rekening = '$accountNumber' WHERE kode_pembelian = '$orderNumber' AND user_id = $userId";
            if (mysqli_query($connection, $query)) {
                echo "Bukti bayar berhasil disimpan!";
            } else {
                echo "Terjadi kesalahan saat menyimpan bukti bayar ke database.";
            }
        } else {
            echo "Kode pemesanan tidak ditemukan!";
        }
    } else {
        echo "Terjadi kesalahan saat mengunggah file.";
    }
} else {
    echo "Tidak ada file yang diupload atau terjadi kesalahan pada file.";
}
?>