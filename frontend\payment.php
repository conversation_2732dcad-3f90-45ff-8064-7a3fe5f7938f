<?php
include 'dbcon.php'; 
$query_admin = "SELECT rekeningBank FROM admin ORDER BY created_at DESC LIMIT 1";
$result_admin = mysqli_query($connection, $query_admin);
$rekeningBank = '';
if ($result_admin && mysqli_num_rows($result_admin) > 0) {
    $admin_data = mysqli_fetch_assoc($result_admin);
    $rekeningBank = htmlspecialchars($admin_data['rekeningBank']);
}

?>
<?php include('header.php'); ?>

<head>
    <link rel="stylesheet" href="css/payment.css">
</head>
<section id="payment">
    <div class="payment-container">
        <h2 class="payment-title">Cara Pembayaran</h2>
        <p class="payment-subtitle">Proses mudah dalam 4 langkah</p>
        <div class="payment-steps">
            <div class="step-item">
                <div class="step-icon"><i class="fas fa-shopping-cart"></i></div>
                <span class="step-label">Masukkan Keran<PERSON>g</span>
            </div>
            <div class="step-item">
                <div class="step-icon"><i class="fas fa-file-invoice"></i></div>
                <span class="step-label">Checkout</span>
            </div>
            <div class="step-item">
                <div class="step-icon"><i class="fas fa-upload"></i></div>
                <span class="step-label">Kirim Bukti Bayar</span>
            </div>
            <div class="step-item">
                <div class="step-icon"><i class="fas fa-check-circle"></i></div>
                <span class="step-label">Konfirmasi</span>
            </div>
        </div>
        <div class="transaction-details">
            <h3>Detail Transaksi</h3>
            <table class="cart-table">
                <thead>
                    <tr>
                        <th>Kode Pembelian</th>
                        <th>Nama Barang</th>
                        <th>Jumlah</th>
                        <th>Harga Satuan</th>
                        <th>Total Harga</th>
                        <th>Status Pembayaran</th>
                        <th>Aksi</th>
                        <th hidden>Harga Beli</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                                $query = "SELECT id, kode_pembelian, nama_barang, jumlah_produk, harga_satuan, total_harga, bukti_bayar
                                        FROM pembelian
                                        WHERE user_id = $userId";
                                $result = mysqli_query($connection, $query);
                                if ($result && mysqli_num_rows($result) > 0) {
                                    while ($row = mysqli_fetch_assoc($result)) {
                                        echo '<tr>';
                                        echo '<td>' . htmlspecialchars($row['kode_pembelian']) . '</td>';
                                        echo '<td>' . htmlspecialchars($row['nama_barang']) . '</td>';
                                        echo '<td>' . $row['jumlah_produk'] . '</td>';
                                        echo '<td>Rp ' . number_format($row['harga_satuan'], 0, ',', '.') . '</td>';
                                        echo '<td>Rp ' . number_format($row['total_harga'], 0, ',', '.') . '</td>';
                                        if (!empty($row['bukti_bayar'])) {
                                            echo '<td>Lunas</td>';
                                        } else {
                                            echo '<td>Belum Bayar</td>';
                                        }
                                        echo '<td><a href="hapus_transaksi.php?id=' . $row['id'] . '" onclick="return confirm(\'Yakin ingin menghapus transaksi ini?\')">Hapus</a></td>';
                                        echo '</tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="6">Tidak ada transaksi ditemukan.</td></tr>';
                                }
                            ?>
                </tbody>
            </table>
        </div>
        <div class="payment-form">
            <h3>Konfirmasi Pembayaran</h3>
            <form id="paymentForm" method="POST" enctype="multipart/form-data">
                <input class="kirim" type="text" name="order_number" placeholder="Nomor Order" required />
                <input type="file" name="bukti_bayar" accept=".jpg, .jpeg, .png" required />
                <div class="payment-methods">
                    <label>Pilih Metode Pembayaran</label>
                    <div class="payment-options">
                        <!-- Bank Virtual Account -->
                        <div class="payment-option" data-method="bca" data-number="**********">
                            <div class="payment-logo">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/5/5c/Bank_Central_Asia.svg" alt="BCA" />
                            </div>
                            <div class="payment-info">
                                <span class="bank-name">BCA Virtual Account</span>
                                <span class="account-number">**********</span>
                            </div>
                        </div>

                        <div class="payment-option" data-method="mandiri" data-number="**********">
                            <div class="payment-logo">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/a/ad/Bank_Mandiri_logo_2016.svg" alt="Mandiri" />
                            </div>
                            <div class="payment-info">
                                <span class="bank-name">Mandiri Virtual Account</span>
                                <span class="account-number">**********</span>
                            </div>
                        </div>

                        <div class="payment-option" data-method="bni" data-number="**********">
                            <div class="payment-logo">
                                <img src="https://upload.wikimedia.org/wikipedia/en/2/27/BNI_logo.svg" alt="BNI" />
                            </div>
                            <div class="payment-info">
                                <span class="bank-name">BNI Virtual Account</span>
                                <span class="account-number">**********</span>
                            </div>
                        </div>

                        <!-- E-Money -->
                        <div class="payment-option" data-method="gopay" data-number="08**********">
                            <div class="payment-logo">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/8/86/Gopay_logo.svg" alt="GoPay" />
                            </div>
                            <div class="payment-info">
                                <span class="bank-name">GoPay</span>
                                <span class="account-number">08**********</span>
                            </div>
                        </div>

                        <div class="payment-option" data-method="ovo" data-number="************">
                            <div class="payment-logo">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/e/eb/Logo_ovo_purple.svg" alt="OVO" />
                            </div>
                            <div class="payment-info">
                                <span class="bank-name">OVO</span>
                                <span class="account-number">************</span>
                            </div>
                        </div>

                        <div class="payment-option" data-method="dana" data-number="************">
                            <div class="payment-logo">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/7/72/Logo_dana_blue.svg" alt="DANA" />
                            </div>
                            <div class="payment-info">
                                <span class="bank-name">DANA</span>
                                <span class="account-number">************</span>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="selected_payment_method" id="selectedPaymentMethod" required />
                    <input type="hidden" name="selected_account_number" id="selectedAccountNumber" required />
                </div>
                <button class="kirim" type="submit">Kirim Bukti Pembayaran</button>
            </form>
        </div>
    </div>
</section>
<?php include('footer.php'); ?>
<script>
// Handle payment method selection
document.querySelectorAll('.payment-option').forEach(option => {
    option.addEventListener('click', function() {
        // Remove active class from all options
        document.querySelectorAll('.payment-option').forEach(opt => opt.classList.remove('active'));

        // Add active class to selected option
        this.classList.add('active');

        // Set hidden input values
        document.getElementById('selectedPaymentMethod').value = this.dataset.method;
        document.getElementById('selectedAccountNumber').value = this.dataset.number;
    });
});

document.getElementById('paymentForm').addEventListener('submit', function(event) {
    event.preventDefault();

    // Check if payment method is selected
    if (!document.getElementById('selectedPaymentMethod').value) {
        alert('Silakan pilih metode pembayaran terlebih dahulu!');
        return;
    }

    let formData = new FormData(this);
    fetch('upload.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            alert(data);
            if (data.includes('Bukti bayar berhasil disimpan!')) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat mengupload bukti pembayaran.');
        });
});
</script>